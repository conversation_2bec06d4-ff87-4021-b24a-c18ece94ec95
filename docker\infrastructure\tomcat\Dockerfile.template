# 
# 注意：这是一个模板文件，不要直接使用
# 在构建时会由构建脚本(build-tomcat.sh)生成实际的Dockerfidocker/infrastructure/tomcat/le
# 
# 基础镜像将由构建脚本替换
ARG BASE_IMAGE
FROM $BASE_IMAGE

# 设置环境变量
ENV TOMCAT_VERSION=$TOMCAT_VERSION \
    TOMCAT_MAJOR=$TOMCAT_MAJOR \
    TOMCAT_VARIANT=$TOMCAT_VARIANT \
    TOMCAT_HOME=/usr/local/tomcat \
    CATALINA_HOME=/usr/local/tomcat \
    CATALINA_BASE=/usr/local/tomcat \
    TOMCAT_NATIVE_LIBDIR=/usr/local/tomcat/native-jni-lib \
    LD_LIBRARY_PATH=/usr/lib64:/usr/local/tomcat/native-jni-lib \
    CRYPTO_FILES_VERSION=$CRYPTO_FILES_VERSION \
    JAVA_HOME=/usr/local/jdk1.8.0_172 \
    PATH=/usr/local/tomcat/bin:$PATH

# 设置工作目录
WORKDIR $CATALINA_HOME

# 设置SHA512校验和（由构建脚本替换）
ENV TOMCAT_SHA512=$TOMCAT_SHA512

# 复制预下载的Tomcat包
COPY docker/infrastructure/tomcat/resources/apache-tomcat-${TOMCAT_VERSION}.tar.gz tomcat.tar.gz

# 验证校验和并解压
RUN set -eux; \
    if [ -n "$TOMCAT_SHA512" ]; then \
        echo "$TOMCAT_SHA512 *tomcat.tar.gz" | sha512sum --strict --check -; \
    fi; \
    tar -xf tomcat.tar.gz --strip-components=1; \
    rm tomcat.tar.gz; \
    \
    # 添加可执行权限
    chmod +x bin/*.sh; \
    \
    # 简单验证安装 - 只检查关键文件是否存在
    echo "验证Tomcat安装..."; \
    if [ ! -f "$CATALINA_HOME/bin/catalina.sh" ] || \
       [ ! -f "$CATALINA_HOME/conf/server.xml" ] || \
       [ ! -f "$CATALINA_HOME/lib/catalina.jar" ] || \
       [ ! -d "$CATALINA_HOME/webapps" ]; then \
        echo "Tomcat安装不完整，缺少关键文件"; \
        exit 1; \
    fi; \
    echo "Tomcat安装验证通过"

# 创建tomcat用户
RUN set -eux; \
    groupadd -r tomcat --gid=1000; \
    useradd -r -g tomcat --uid=1000 --home-dir=/usr/local/tomcat --shell=/bin/false tomcat

# Copy NetcaJCrypto jar files (only for crypto variant)
COPY docker/infrastructure/tomcat/shared/crypto-files/slf4j* $CATALINA_HOME/lib/
COPY docker/infrastructure/tomcat/shared/crypto-files/NetcaJCrypto* $CATALINA_HOME/lib/

# 仅复制自定义的server.xml配置文件
COPY docker/infrastructure/tomcat/shared/conf/server.xml $CATALINA_HOME/conf/server.xml

# 设置所有权和权限
RUN set -eux; \
    # 确保所有文件属于tomcat用户
    chown -R tomcat:tomcat /usr/local/tomcat; \
    # 设置目录权限：所有者可读写执行，组和其他用户可读执行
    find /usr/local/tomcat -type d -exec chmod 755 {} \;; \
    # 设置文件权限：所有者可读写，组和其他用户可读，脚本文件可执行
    find /usr/local/tomcat -type f -exec chmod 644 {} \;; \
    # 确保所有.sh脚本文件可执行
    find /usr/local/tomcat -name "*.sh" -exec chmod 755 {} \;; \
    # 设置特殊目录权限（临时目录需要写权限）
    chmod 1777 logs temp work

# 添加标签信息
LABEL maintainer="Medical Products Team" \
      description="Tomcat $TOMCAT_VERSION server with $JDK_VERSION for medical products" \
      version="$TOMCAT_VERSION" \
      build.date="$BUILD_DATE" \
      vendor="BTIT" \
      architecture="$ARCH" \
      base.image="$BASE_IMAGE" \
      java.type="$JAVA_TYPE" \
      java.distribution="$JAVA_DISTRIBUTION" \
      os.codename="$OS_CODENAME"

# 暴露端口
EXPOSE 8080 443 8443 8005 8009

# 设置健康检查
HEALTHCHECK --interval=30s --timeout=3s \
  CMD curl -f http://localhost:8080/ || exit 1

# 启动Tomcat
USER tomcat
CMD ["/usr/local/tomcat/bin/catalina.sh", "run"]

